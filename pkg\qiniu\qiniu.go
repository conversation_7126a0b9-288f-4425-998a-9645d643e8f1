package qiniu

import (
	"bytes"
	"chongli/component/apollo"
	"context"
	"encoding/base64"
	"fmt"
	"mime/multipart"
	"os"
	"strings"

	"github.com/gookit/goutil/strutil"
	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/cdn"
	"github.com/qiniu/go-sdk/v7/storage"
)

const (
	qiNiuPrefix = "chongli/"
)

// UploadFile 上传文件到七牛云存储
func UploadFile(uid int64, file *multipart.FileHeader, fileName string) (string, error) {
	// 获取Apollo配置。
	apolloConfig := apollo.GetApolloConfig()
	// 初始化存储桶范围。
	scope := apolloConfig.QiniuyunBucket
	path := ""
	// 如果指定了文件名，则构建对应的存储路径。
	if fileName != "" {
		path = qiNiuPrefix + fileName
		scope = apolloConfig.QiniuyunBucket + ":" + path
	} else {
		// 如果未指定文件名，则生成随机文件名。
		randomChars := strutil.RandomCharsV2(24)
		fileSuffix := formatFileSuffix(file)
		if uid != 0 {
			path = fmt.Sprintf("%s%s_%d.%s", qiNiuPrefix, randomChars, uid, fileSuffix)
		} else {
			path = fmt.Sprintf("%s%s.%s", qiNiuPrefix, randomChars, fileSuffix)
		}
	}
	// 初始化上传策略。
	putPolicy := storage.PutPolicy{
		Scope: scope,
	}
	putPolicy.InsertOnly = 0
	// 创建七牛云凭证。
	mac := qbox.NewMac(apolloConfig.QiniuyunAccessKey, apolloConfig.QiniuyunSecretKey)
	// 生成上传令牌。
	upToken := putPolicy.UploadToken(mac)
	// 初始化上传配置。
	cfg := storage.Config{}
	cfg.UseHTTPS = true
	cfg.UseCdnDomains = true
	// 创建表单上传实例。
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{
		Params: map[string]string{},
	}
	// 打开文件以供上传。
	data, err := file.Open()
	if err != nil {
		return "", err
	}
	defer func() {
		_ = data.Close()
	}()
	// 执行上传操作。
	err = formUploader.Put(context.Background(), &ret, upToken, path, data, file.Size, &putExtra)
	if err != nil {
		return "", err
	}
	// 如果指定了文件名，则刷新CDN缓存。
	if fileName != "" {
		cdnManager := cdn.NewCdnManager(qbox.NewMac(apolloConfig.QiniuyunAccessKey, apolloConfig.QiniuyunSecretKey))
		_, _ = cdnManager.RefreshUrls([]string{fmt.Sprintf("%s/%s", apolloConfig.QiniuyunDomain, ret.Key)})
	}
	// 返回上传后的文件URL。
	return fmt.Sprintf("%s/%s", apolloConfig.QiniuyunDomain, ret.Key), nil
}

// formatFileSuffix 获取文件后缀名
func formatFileSuffix(file *multipart.FileHeader) string {
	arr := strings.Split(file.Filename, ".")
	return arr[len(arr)-1]
}

// UploadLocalFile 上传本地文件到七牛云存储
func UploadLocalFile(localFilePath, qiniuPath string) (string, error) {
	// 获取Apollo配置
	apolloConfig := apollo.GetApolloConfig()

	// 打开本地文件
	file, err := os.Open(localFilePath)
	if err != nil {
		return "", fmt.Errorf("打开本地文件失败: %w", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return "", fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 初始化存储桶范围
	scope := apolloConfig.QiniuyunBucket + ":" + qiniuPath

	// 初始化上传策略
	putPolicy := storage.PutPolicy{
		Scope: scope,
	}
	putPolicy.InsertOnly = 0

	// 创建七牛云凭证
	mac := qbox.NewMac(apolloConfig.QiniuyunAccessKey, apolloConfig.QiniuyunSecretKey)
	upToken := putPolicy.UploadToken(mac)

	// 初始化上传配置
	cfg := storage.Config{}
	cfg.UseHTTPS = true
	cfg.UseCdnDomains = true

	// 创建表单上传实例
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{
		Params: map[string]string{},
	}

	// 执行上传操作
	err = formUploader.Put(context.Background(), &ret, upToken, qiniuPath, file, fileInfo.Size(), &putExtra)
	if err != nil {
		return "", err
	}

	// 返回上传后的文件URL
	return fmt.Sprintf("%s/%s", apolloConfig.QiniuyunDomain, ret.Key), nil
}

// UploadBase64ToQiniu uploads a base64 encoded image to Qiniu
func UploadBase64ToQiniu(base64Data string, qiniuPath string) (string, error) {
	apolloConfig := apollo.GetApolloConfig()

	// Decode base64 string
	data, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 data: %w", err)
	}

	// Initialize storage scope
	scope := apolloConfig.QiniuyunBucket + ":" + qiniuPath

	// Initialize upload policy
	putPolicy := storage.PutPolicy{
		Scope: scope,
	}
	putPolicy.InsertOnly = 0

	// Create Qiniu credentials
	mac := qbox.NewMac(apolloConfig.QiniuyunAccessKey, apolloConfig.QiniuyunSecretKey)
	upToken := putPolicy.UploadToken(mac)

	// Initialize upload config
	cfg := storage.Config{}
	cfg.UseHTTPS = true
	cfg.UseCdnDomains = true

	// Create form uploader instance
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{
		Params: map[string]string{},
	}

	// Execute upload operation
	err = formUploader.Put(context.Background(), &ret, upToken, qiniuPath, bytes.NewReader(data), int64(len(data)), &putExtra)
	if err != nil {
		return "", err
	}

	// Return the uploaded file URL
	return fmt.Sprintf("%s/%s", apolloConfig.QiniuyunDomain, ret.Key), nil
}
