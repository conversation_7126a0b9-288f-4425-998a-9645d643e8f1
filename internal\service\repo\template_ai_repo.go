package repo

import (
	"context"

	"chongli/internal/service/dto"

	"gorm.io/gorm"
)

// TemplateAIRepo AI模板仓库接口
type TemplateAIRepo interface {
	// Create 创建AI模板
	Create(ctx context.Context, template *dto.TemplateAIDTO, tx ...*gorm.DB) error

	// Update 更新AI模板
	Update(ctx context.Context, template *dto.TemplateAIDTO, tx ...*gorm.DB) error

	// Delete 删除AI模板
	Delete(ctx context.Context, id uint64, tx ...*gorm.DB) error

	// GetByID 根据ID获取AI模板，支持联表查询模板分类
	GetByID(ctx context.Context, id uint64, tx ...*gorm.DB) (*dto.TemplateAIDTO, error)

	// GetByQuery 根据条件查询单个AI模板，支持联表查询模板分类
	GetByQuery(ctx context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) (*dto.TemplateAIQueryDTO, error)

	// ListByQuery 根据条件查询AI模板列表，支持联表查询模板分类
	ListByQuery(ctx context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateAIDTO, error)

	// ListAll 查询所有AI模板，支持联表查询模板分类
	ListAll(ctx context.Context, tx ...*gorm.DB) ([]*dto.TemplateAIDTO, error)

	// Count 根据条件统计AI模板数量
	Count(ctx context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) (int64, error)
}
