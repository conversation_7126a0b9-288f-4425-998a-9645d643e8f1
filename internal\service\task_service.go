package service

import (
	"context"
	"fmt"
	"time"

	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type TaskService struct {
	db           driver.ITransaction
	taskRepo     repo.TaskRepo
	taskStepRepo repo.TaskStepRepo
}

func NewTaskService(
	component *component.BootStrap,
	taskRepo repo.TaskRepo,
	taskStepRepo repo.TaskStepRepo,
) *TaskService {
	return &TaskService{
		db:           component.Tx,
		taskRepo:     taskRepo,
		taskStepRepo: taskStepRepo,
	}
}

// CreateTaskWithSteps 事务性地创建任务和步骤，可选外部传入事务
func (s *TaskService) CreateTaskWithSteps(ctx context.Context, req *dto.CreateTaskRequest, tx ...*gorm.DB) (*dto.TaskWithStepsResponse, error) {
	var task *model.Task
	var steps []*model.TaskStep

	// 如果外部传入了事务，直接使用；否则创建新事务
	if len(tx) > 0 && tx[0] != nil {
		// 使用外部事务
		err := s.createTaskWithStepsInTx(ctx, req, tx[0])
		if err != nil {
			return nil, err
		}
	} else {
		// 创建新事务
		err := s.db.MysqlDbTxBegin().Transaction(func(newTx *gorm.DB) error {
			return s.createTaskWithStepsInTx(ctx, req, newTx)
		})
		if err != nil {
			return nil, err
		}
	}

	return &dto.TaskWithStepsResponse{
		Task:  s.taskToDTO(task),
		Steps: s.stepsToDTO(steps),
	}, nil
}

// createTaskWithStepsInTx 在指定事务中创建任务和步骤的内部实现
func (s *TaskService) createTaskWithStepsInTx(ctx context.Context, req *dto.CreateTaskRequest, tx *gorm.DB) error {
	task := &dto.TaskDTO{
		TaskType:    req.TaskType,
		UserID:      req.UserID,
		CurrentStep: 0,
		RetryCount:  0,
		DeviceID:    req.DeviceID,
	}
	newTask, err := s.taskRepo.Create(ctx, task, tx)
	if err != nil {
		return fmt.Errorf("创建任务失败: %w", err)
	}

	// 转换为 DTO
	stepDTOs := make([]*dto.TaskStepDTO, 0)
	for i, stepReq := range req.Steps {
		step := &dto.TaskStepDTO{
			TaskID:     newTask.ID,
			StepIndex:  i,
			StepName:   stepReq.StepName,
			Status:     stepReq.Status,
			Params:     dto.JSONMap(stepReq.Params),
			RetryCount: 0,
		}
		stepDTOs = append(stepDTOs, step)
	}

	if err := s.taskStepRepo.CreateBatch(ctx, stepDTOs, tx); err != nil {
		return fmt.Errorf("创建任务步骤失败: %w", err)
	}

	return nil
}

// GetNextPendingStep 事务性地获取要执行的任务步骤，可选外部传入事务
func (s *TaskService) GetNextPendingStep(ctx context.Context, taskID int64, tx ...*gorm.DB) (*dto.TaskStepDTO, error) {
	var step *dto.TaskStepDTO

	// 如果外部传入了事务，直接使用；否则创建新事务
	if len(tx) > 0 && tx[0] != nil {
		// 使用外部事务
		err := s.getNextPendingStepInTx(ctx, taskID, step, tx[0])
		if err != nil {
			return nil, err
		}
	} else {
		// 创建新事务
		err := s.db.MysqlDbTxBegin().Transaction(func(newTx *gorm.DB) error {
			return s.getNextPendingStepInTx(ctx, taskID, step, newTx)
		})
		if err != nil {
			return nil, err
		}
	}

	return step, nil
}

// getNextPendingStepInTx 在指定事务中获取下一个待执行步骤的内部实现
func (s *TaskService) getNextPendingStepInTx(ctx context.Context, taskID int64, step *dto.TaskStepDTO, tx *gorm.DB) error {
	// // 获取任务信息
	// task, err := s.taskRepo.GetByID(ctx, taskID, tx)
	// if err != nil {
	// 	return fmt.Errorf("获取任务失败: %w", err)
	// }

	// // 检查任务状态
	// if task.Status == "done" || task.Status == "failed" {
	// 	return fmt.Errorf("任务已完成或失败，无法获取下一步骤")
	// }

	// // 获取下一个待执行的步骤
	// limit := 1
	// query := &dto.GetStepWithTaskQuery{
	// 	Step: &dto.TaskStepDTO{
	// 		TaskID: taskID,
	// 		Status: model.StepStatusPending,
	// 	},
	// 	Limit: &limit,
	// }

	// stepDTOs, err := s.taskStepRepo.GetStepWithTask(ctx, query, tx)
	// if err != nil {
	// 	return fmt.Errorf("获取待执行步骤失败: %w", err)
	// }

	// if len(stepDTOs) == 0 {
	// 	return fmt.Errorf("没有待执行的步骤")
	// }

	// // 将 DTO 转换为 model
	// stepDTO := stepDTOs[0]
	// *step = &model.TaskStep{
	// 	ID:         stepDTO.ID,
	// 	TaskID:     stepDTO.TaskID,
	// 	StepIndex:  stepDTO.StepIndex,
	// 	StepName:   stepDTO.StepName,
	// 	Status:     stepDTO.Status,
	// 	RetryCount: stepDTO.RetryCount,
	// 	Params:     model.JSONMap(stepDTO.Params),
	// 	Result:     model.JSONMap(stepDTO.Result),
	// 	ErrorMsg:   stepDTO.ErrorMsg,
	// 	StartedAt:  stepDTO.StartedAt,
	// 	FinishedAt: stepDTO.FinishedAt,
	// 	CreatedAt:  stepDTO.CreatedAt,
	// 	UpdatedAt:  stepDTO.UpdatedAt,
	// }

	// // 更新步骤状态为运行中
	// now := time.Now()
	// (*step).Status = model.StepStatusRunning
	// (*step).StartedAt = &now

	// // 创建更新字段的map
	// updates := map[string]interface{}{
	// 	"status":     (*step).Status,
	// 	"started_at": (*step).StartedAt,
	// }
	// if err := s.taskStepRepo.Update(ctx, (*step).ID, updates, tx); err != nil {
	// 	return fmt.Errorf("更新步骤状态失败: %w", err)
	// }

	// // 更新任务状态为运行中
	// task.Status = "running"
	// task.CurrentStep = (*step).StepIndex

	// if err := s.taskRepo.Update(ctx, task.ID, map[string]interface{}{
	// 	"status":       "running",
	// 	"current_step": (*step).StepIndex,
	// }, tx); err != nil {
	// 	return fmt.Errorf("更新任务状态失败: %w", err)
	// }

	return nil
}

// CompleteStepAndCheckTask 事务性地完成某个步骤，如果任务已完成则设置为完成，可选外部传入事务
func (s *TaskService) CompleteStepAndCheckTask(ctx context.Context, req *dto.CompleteStepRequest, tx ...*gorm.DB) error {
	// 如果外部传入了事务，直接使用；否则创建新事务
	if len(tx) > 0 && tx[0] != nil {
		// 使用外部事务
		return s.completeStepAndCheckTaskInTx(ctx, req, tx[0])
	} else {
		// 创建新事务
		tx := s.db.MysqlDbTxBegin()
		txErr := s.completeStepAndCheckTaskInTx(ctx, req, tx)
		if txErr != nil {
			tx.Rollback()
			return txErr
		}
		tx.Commit()
		return nil
	}
}

// completeStepAndCheckTaskInTx 在指定事务中完成步骤并检查任务状态的内部实现
func (s *TaskService) completeStepAndCheckTaskInTx(ctx context.Context, req *dto.CompleteStepRequest, tx *gorm.DB) error {
	// 获取步骤信息
	query := &dto.GetStepWithTaskQuery{
		Step: &dto.TaskStepDTO{
			ID: req.StepID,
		},
	}

	stepDTOs, err := s.taskStepRepo.GetStepWithTask(ctx, query, tx)
	if err != nil {
		return fmt.Errorf("获取步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return fmt.Errorf("步骤不存在")
	}

	// 将 DTO 转换为 model
	stepDTO := stepDTOs[0]

	// 更新步骤状态
	now := time.Now()
	stepDTO.FinishedAt = &now
	// stepDTO.Result = dto.JSONMap(req.Result)

	if req.ErrorMsg != "" {
		stepDTO.Status = model.StepStatusFailed
		stepDTO.ErrorMsg = req.ErrorMsg
	} else {
		stepDTO.Status = model.StepStatusDone
	}

	// 更新当前步骤失败
	updates := map[string]any{
		"status":      stepDTO.Status,
		"error_msg":   stepDTO.ErrorMsg,
		"finished_at": time.Now(),
	}
	if err := s.taskStepRepo.Update(ctx, stepDTO.ID, updates, tx); err != nil {
		return fmt.Errorf("更新步骤状态失败: %w", err)
	}

	// 获取任务信息
	task, err := s.taskRepo.GetByID(ctx, stepDTO.TaskID, tx)
	if err != nil {
		return fmt.Errorf("获取任务失败: %w", err)
	}

	// 如果步骤失败，标记任务失败
	if stepDTO.Status == model.StepStatusFailed {
		task.Status = model.StepStatusFailed
		task.ErrorMsg = req.ErrorMsg
		return s.taskRepo.Update(ctx, task.ID, map[string]interface{}{
			"status":    model.StepStatusFailed,
			"error_msg": req.ErrorMsg,
		}, tx)
	}

	return nil
}

// CompleteTask 事务性地完成任务，可选外部传入事务
func (s *TaskService) CompleteTask(ctx context.Context, taskID int64, errorMsg string, tx ...*gorm.DB) error {
	// 如果外部传入了事务，直接使用；否则创建新事务
	if len(tx) > 0 && tx[0] != nil {
		// 使用外部事务
		return s.completeTaskInTx(ctx, taskID, errorMsg, tx[0])
	} else {
		// 创建新事务
		return s.db.MysqlDbTxBegin().Transaction(func(newTx *gorm.DB) error {
			return s.completeTaskInTx(ctx, taskID, errorMsg, newTx)
		})
	}
}

// completeTaskInTx 在指定事务中完成任务的内部实现
func (s *TaskService) completeTaskInTx(ctx context.Context, taskID int64, errorMsg string, tx *gorm.DB) error {
	// 获取任务信息
	task, err := s.taskRepo.GetByID(ctx, taskID, tx)
	if err != nil {
		return fmt.Errorf("获取任务失败: %w", err)
	}

	// 更新任务状态
	if errorMsg != "" {
		task.Status = "failed"
		task.ErrorMsg = errorMsg
	} else {
		task.Status = "done"
	}

	return s.taskRepo.Update(ctx, task.ID, map[string]interface{}{
		"status":    task.Status,
		"error_msg": task.ErrorMsg,
	}, tx)
}

// GetPendingTaskStep 根据任务类型获取一个待执行的任务步骤，获取后将步骤状态更改为运行中，可选外部传入事务
func (s *TaskService) GetPendingTaskStep(ctx context.Context, taskType string, tx ...*gorm.DB) (*dto.TaskStepDTO, error) {
	var finalStepDTO *dto.TaskStepDTO

	// 如果外部传入了事务，直接使用；否则创建新事务
	if len(tx) > 0 && tx[0] != nil {
		// 使用外部事务
		var err error
		finalStepDTO, err = s.getPendingTaskStepInTx(ctx, taskType, tx...)
		if err != nil {
			return nil, err
		}
	} else {
		// 创建新事务
		tx := s.db.MysqlDbTxBegin()
		var txErr error
		finalStepDTO, txErr = s.getPendingTaskStepInTx(ctx, taskType, tx)
		if txErr != nil {
			tx.Rollback()
			return nil, txErr
		}
		tx.Commit()
	}

	return finalStepDTO, nil
}

// getPendingTaskStepInTx 在指定事务中根据任务类型获取待执行步骤并更新状态的内部实现
func (s *TaskService) getPendingTaskStepInTx(ctx context.Context, stepType string, tx ...*gorm.DB) (*dto.TaskStepDTO, error) {
	// 构建查询条件：获取指定任务类型的第一个待执行步骤
	limit := 1
	query := &dto.GetStepWithTaskQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusPending,
			StepName: stepType,
		},
		Limit: &limit,
	}

	// 通过 DAO 层查找步骤
	stepDTOs, err := s.taskStepRepo.GetStepWithTask(ctx, query, tx...)
	if err != nil {
		return nil, fmt.Errorf("获取待执行步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, fmt.Errorf("没有类型为 %s 的待执行步骤", stepType)
	}
	stepDTO := stepDTOs[0]

	// 更新步骤状态为运行中
	now := time.Now()
	stepDTO.Status = model.StepStatusRunning
	stepDTO.StartedAt = &now

	// 创建更新字段的map
	updates := map[string]any{
		"status":     stepDTO.Status,
		"started_at": stepDTO.StartedAt,
	}
	if err := s.taskStepRepo.Update(ctx, stepDTO.ID, updates, tx...); err != nil {
		return nil, fmt.Errorf("更新步骤状态失败: %w", err)
	}

	// 获取关联的任务
	task, err := s.taskRepo.GetByID(ctx, stepDTO.TaskID, tx...)
	if err != nil {
		return nil, fmt.Errorf("获取任务失败: %w", err)
	}

	// 如果任务状态是 pending，更新为 running
	if task.Status == "pending" {
		task.Status = "running"
		task.CurrentStep = stepDTO.StepIndex
		if err := s.taskRepo.Update(ctx, task.ID, map[string]interface{}{
			"status":       task.Status,
			"current_step": task.CurrentStep,
		}, tx...); err != nil {
			return nil, fmt.Errorf("更新任务状态失败: %w", err)
		}
	}

	return stepDTO, nil
}

// GetStepWithTask 根据查询条件获取步骤信息，可选外部传入事务
func (s *TaskService) GetStepWithTask(ctx context.Context, query *dto.GetStepWithTaskQuery, tx ...*gorm.DB) ([]*dto.TaskStepDTO, error) {
	return s.taskStepRepo.GetStepWithTask(ctx, query, tx...)
}

// taskToDTO 将 model.Task 转换为 dto.TaskDTO
func (s *TaskService) taskToDTO(task *model.Task) *dto.TaskDTO {
	if task == nil {
		return nil
	}
	return &dto.TaskDTO{
		ID:          task.ID,
		TaskType:    task.TaskType,
		Status:      task.Status,
		CurrentStep: task.CurrentStep,
		RetryCount:  task.RetryCount,
		ErrorMsg:    task.ErrorMsg,
		CreatedAt:   task.CreatedAt,
		UpdatedAt:   task.UpdatedAt,
	}
}

// taskStepToDTO 将 model.TaskStep 转换为 dto.TaskStepDTO
func (s *TaskService) taskStepToDTO(step *model.TaskStep) *dto.TaskStepDTO {
	if step == nil {
		return nil
	}
	return &dto.TaskStepDTO{
		ID:         step.ID,
		TaskID:     step.TaskID,
		StepIndex:  step.StepIndex,
		StepName:   step.StepName,
		Status:     step.Status,
		RetryCount: step.RetryCount,
		Params:     dto.JSONMap(step.Params),
		Result:     dto.JSONMap(step.Result),
		ErrorMsg:   step.ErrorMsg,
		StartedAt:  step.StartedAt,
		FinishedAt: step.FinishedAt,
		CreatedAt:  step.CreatedAt,
		UpdatedAt:  step.UpdatedAt,
	}
}

// stepsToDTO 将 []*model.TaskStep 转换为 []*dto.TaskStepDTO
func (s *TaskService) stepsToDTO(steps []*model.TaskStep) []*dto.TaskStepDTO {
	if steps == nil {
		return nil
	}
	dtoSteps := make([]*dto.TaskStepDTO, len(steps))
	for i, step := range steps {
		dtoSteps[i] = s.taskStepToDTO(step)
	}
	return dtoSteps
}

// UpdateStepStatusAndResult 更新步骤状态和结果
func (s *TaskService) UpdateStepStatusAndResult(ctx context.Context, stepID int64, status string, result []byte, tx ...*gorm.DB) error {
	// 如果外部传入了事务，直接使用；否则创建新事务
	if len(tx) > 0 && tx[0] != nil {
		// 使用外部事务
		return s.updateStepStatusAndResultInTx(ctx, stepID, status, result, tx[0])
	} else {
		tx := s.db.MysqlDbTxBegin()
		// 创建新事务
		err := s.updateStepStatusAndResultInTx(ctx, stepID, status, result, tx)
		if err != nil {
			tx.Rollback()
			return err
		}
		tx.Commit()
		return nil
	}
}

// updateStepStatusAndResultInTx 在指定事务中更新步骤状态和结果的内部实现
func (s *TaskService) updateStepStatusAndResultInTx(ctx context.Context, stepID int64, status string, result []byte, tx *gorm.DB) error {
	now := time.Now()
	updates := map[string]any{
		"status":     status,
		"result":     datatypes.JSON(result),
		"updated_at": now,
	}

	// 如果状态是 waiting_result，设置开始时间；如果是完成状态，设置完成时间
	switch status {
	case model.StepStatusWaitingResult:
		updates["started_at"] = now
	case model.StepStatusDone, model.StepStatusFailed:
		updates["finished_at"] = now
	}

	err := s.taskStepRepo.Update(ctx, stepID, updates, tx)
	if err != nil {
		return fmt.Errorf("更新任务步骤失败: %w", err)
	}

	return nil
}
