package dto

import (
	"encoding/json"
	"time"
)

// TemplateAIDTO AI模板数据传输对象
type TemplateAIDTO struct {
	ID            uint64           `json:"id"`
	Name          string           `json:"name"`
	CoverURL      string           `json:"cover_url"`
	VideoCoverURL string           `json:"video_cover_url"`
	Status        int8             `json:"status"`
	SortOrder     int              `json:"sort_order"`
	MaxVersionInt int64            `json:"max_version_int"` // 最大适用版本int值
	CategoryID    uint64           `json:"category_id"`
	VariablesJSON *json.RawMessage `json:"variables_json"`
	Description   string           `json:"description"`
	CreateAt      time.Time        `json:"create_at"`
	UpdatedAt     time.Time        `json:"update_at"`
	DiamondCost   int              `json:"diamond_cost"` // 钻石花费
	Category      TemplateCategoryDto
	Template      string `json:"template"`
}

type TemplateDto struct {
	ID            uint64    `json:"id"`
	Name          string    `json:"name"`
	CoverURL      string    `json:"cover_url"`
	VideoCoverURL string    `json:"video_cover_url"`
	Status        int8      `json:"status"`
	SortOrder     int       `json:"sort_order"`
	MaxVersionInt int64     `json:"max_version_int"` // 最大适用版本int值
	CategoryID    uint64    `json:"category_id"`
	Description   string    `json:"description"`
	CreateAt      time.Time `json:"create_at"`
	UpdatedAt     time.Time `json:"update_at"`
	DiamondCost   int       `json:"diamond_cost"` // 钻石花费
}

type AIPicTemplate struct {
	// 宠物描述
	PetDesc string `json:"pet_desc"`
	// 背景描述
	BackgroundDesc string `json:"background_desc"`
	// 构图描述
	CompositionDesc string `json:"composition_desc"`
	// 风格选择
	Style string `json:"style"`
	// 风格描述
	StyleDesc string `json:"style_desc"`
	// 模型强度
	ModelStrength float64 `json:"strength"`
}

// TemplateAIQueryDTO 查询AI模板的参数
type TemplateAIQueryDTO struct {
	Template TemplateAIDTO

	Category TemplateCategoryDto

	// 分页参数
	PageNum  int `json:"page_num"`
	PageSize int `json:"page_size"`

	// 排序参数
	OrderBy        string `json:"order_by,omitempty"`        // 排序字段
	OrderDirection string `json:"order_direction,omitempty"` // 排序方向：asc/desc
}
