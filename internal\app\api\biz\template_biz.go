package biz

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"github.com/gin-gonic/gin"
)

type TemplateBiz struct {
	log          *logger.Logger
	templateRepo repo.TemplateAIRepo
}

func NewTemplateBiz(
	bootStrap *component.BootStrap,
	templateRepo repo.TemplateAIRepo,
) *TemplateBiz {
	return &TemplateBiz{
		log:          bootStrap.Log,
		templateRepo: templateRepo,
	}
}

func (t *TemplateBiz) GetTemplateList(ctx *gin.Context, templateCategoryId, maxVersionInt int64) ([]*dto.TemplateAIDTO, error) {
	// 获取模板列表
	templates, err := t.templateRepo.ListByQuery(ctx, &dto.TemplateAIQueryDTO{
		Template: dto.TemplateAIDTO{
			CategoryID:    uint64(templateCategoryId),
			MaxVersionInt: maxVersionInt,
		},
	})
	if err != nil {
		t.log.Error("获取模板列表失败: %v", err)
		return nil, err
	}
	if len(templates) == 0 {
		t.log.Warning("该分类没有模板")
		return make([]*dto.TemplateAIDTO, 0), nil
	}
	// 返回模板列表
	return templates, nil
}

func (t *TemplateBiz) GetTemplateDetail(ctx *gin.Context, templateId uint64) (*dto.TemplateAIDTO, error) {
	// 获取模板详情
	template, err := t.templateRepo.GetByID(ctx, templateId)
	if err != nil {
		t.log.Error("获取模板详情失败: %v", err)
		return nil, err
	}
	if template == nil {
		t.log.Warning("模板不存在: %d", templateId)
		return nil, nil
	}
	// 返回模板详情
	return template, nil
}
