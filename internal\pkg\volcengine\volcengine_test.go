package volcengine

import (
	"testing"
)

func TestNewClient(t *testing.T) {
	config := &Config{
		AccessKey: "test_ak",
		SecretKey: "test_sk",
		Region:    "cn-north-1",
	}

	client := NewClient(config)
	if client == nil {
		t.Error("Expected client to be created, got nil")
	}

	if client.config.AccessKey != config.AccessKey {
		t.<PERSON>("Expected AccessKey %s, got %s", config.AccessKey, client.config.AccessKey)
	}
}

func TestCVSync2AsyncSubmitTask(t *testing.T) {
	config := &Config{
		AccessKey: "test_ak",
		SecretKey: "test_sk",
	}

	client := NewClient(config)

	req := &CVSync2AsyncSubmitTaskRequest{
		ReqKey: "test_req_key",
	}

	// 注意：这个测试需要真实的AK/SK才能通过
	// 在实际环境中，你需要替换为真实的凭证
	_, err := client.CVSync2AsyncSubmitTask(req)
	if err != nil {
		t.Logf("Expected error with test credentials: %v", err)
	}
}

func TestCVSync2AsyncGetResult(t *testing.T) {
	config := &Config{
		AccessKey: "test_ak",
		SecretKey: "test_sk",
	}

	client := NewClient(config)

	req := &CVSync2AsyncGetResultRequest{
		ReqKey: "test_req_key",
		TaskID: "test_task_id",
	}

	// 注意：这个测试需要真实的AK/SK才能通过
	// 在实际环境中，你需要替换为真实的凭证
	_, err := client.CVSync2AsyncGetResult(req)
	if err != nil {
		t.Logf("Expected error with test credentials: %v", err)
	}
}

func TestCVSync2AsyncSubmitTaskWithParams(t *testing.T) {
	config := &Config{
		AccessKey: "test_ak",
		SecretKey: "test_sk",
	}

	client := NewClient(config)

	extraParams := map[string]interface{}{
		"image_urls": []string{"http://example.com/image.jpg"},
		"timeout":    30,
	}

	// 注意：这个测试需要真实的AK/SK才能通过
	// 在实际环境中，你需要替换为真实的凭证
	_, err := client.CVSync2AsyncSubmitTaskWithParams("test_req_key", extraParams)
	if err != nil {
		t.Logf("Expected error with test credentials: %v", err)
	}
}

func TestFormatResponse(t *testing.T) {
	testData := map[string]interface{}{
		"status": 200,
		"data":   "test_data",
	}

	result := FormatResponse(testData)
	expected := `{"data":"test_data","status":200}`

	if result != expected {
		t.Errorf("Expected %s, got %s", expected, result)
	}
}
