package getui

import (
	"chongli/pkg/utils"
	"testing"
	"time"
)

func TestPushMsgByCid(t *testing.T) {
	requestId := utils.CreateUUid()
	cid := "29798f2270c0f143a3072193a203e85b"
	msg := &Transmission{
		Type: MakePhoto,
		Payload: MakePhotoPayload{
			PhotoId:    1,
			PhotoCover: "test photo cover",
			State:      1,
			ErrMsg:     "test err msg",
			CreateAt:   time.Now().UnixMilli(),
		},
		Msg:  "写真制作成功",
		Time: time.Now().UnixMilli(),
	}
	ttl := 3000
	respData, err := PushMsgByCid(requestId, cid, msg, ttl)
	if err != nil {
		t.Error(err)
	} else {
		t.Log(respData)
	}
}
