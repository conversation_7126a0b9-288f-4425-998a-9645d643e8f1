package registry

import (
	"chongli/component"
	"chongli/internal/app/task/processors"

	"github.com/robfig/cron/v3"
)

type PicTask struct {
	bootstrap         *component.BootStrap
	PicTaskProcessors *processors.PicTaskProcessors
}

func NewPicTask(c *cron.Cron, bootstrap *component.BootStrap, p *processors.PicTaskProcessors) *PicTask {
	picTask := &PicTask{
		bootstrap:         bootstrap,
		PicTaskProcessors: p,
	}

	// 注册定时任务
	// 每天凌晨四点清理过期订单定时任务
	//_, _ = c.AddFunc("0 0 4 * * *", picTask.payOrderTaskService.CleanExpiredOrders)

	// 测试用，每10秒执行一次
	// _, _ = c.AddFunc("@every 10s", picTask.PicTaskProcessors.AiCompound)
	// _, _ = c.AddFunc("@every 1s", picTask.PicTaskProcessors.ChangeStyle)
	_, _ = c.AddFunc("@every 1s", picTask.PicTaskProcessors.ChangeFace)
	return picTask
}
