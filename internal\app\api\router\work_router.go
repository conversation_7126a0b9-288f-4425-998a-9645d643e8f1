package router

// #region 所有和作品相关的路由

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type TemplateRouter struct {
	templateCategoryCtrl *controller.TemplateCategoryController
	templateCtrl         *controller.TemplateController
	userWorkCtrl         *controller.UserWorksController
}

func NewTemplateRouter(
	engine *gin.Engine,
	templateCategoryCtrl *controller.TemplateCategoryController,
	templateCtrl *controller.TemplateController,
	userWorkCtrl *controller.UserWorksController,
) *TemplateRouter {
	router := &TemplateRouter{
		templateCategoryCtrl: templateCategoryCtrl,
		templateCtrl:         templateCtrl,
		userWorkCtrl:         userWorkCtrl,
	}
	template := engine.Group("api/template")
	router.initTemplateCategory(template)
	router.initTemplate(template)
	work := engine.Group("api/work")
	work.Use(middleware.JWTAuth())
	router.initWork(work)
	return router
}

func (r *TemplateRouter) initTemplateCategory(template *gin.RouterGroup) {
	category := template.Group("category")
	// 获取模板分类列表
	category.GET("list/:id", r.templateCategoryCtrl.GetTemplateCategoryList)
}

func (r *TemplateRouter) initTemplate(template *gin.RouterGroup) {
	// 获取模板列表
	template.GET("list/:id", r.templateCtrl.GetTemplateList)
	// 获取模板详情
	template.GET("detail/:id", r.templateCtrl.GetTemplateDetail)
}

func (r *TemplateRouter) initWork(work *gin.RouterGroup) {

	work.POST("make", r.userWorkCtrl.CreatePicWorks)
}
