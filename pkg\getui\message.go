package getui

// Transmission 消息体
type Transmission struct {
	Type    string `json:"type"` // 消息类型
	Payload any    `json:"data"` // 消息载荷
	Msg     string `json:"msg"`  // 消息内容
	Time    int64  `json:"time"` // 消息时间戳
}

// MakePhotoPayload 制作写真消息载荷
type MakePhotoPayload struct {
	PhotoId    uint64 `json:"photo_id"`    // 写真id
	PhotoCover string `json:"photo_cover"` // 写真封面图片地址
	State      int    `json:"state"`       // 写真制作结果;1:制作成功;2:制作失败
	ErrMsg     string `json:"err_msg"`     // 写真制作失败时携带的原因
	CreateAt   int64  `json:"create_at"`   // 写真制作时间
}

// MakeSingingPayload 制作唱歌作品消息载荷
type MakeSingingPayload struct {
	SingingID    int64  `json:"singing_id"`  // 唱歌作品id
	SingingCover string `json:"photo_cover"` // 唱歌作品封面图片地址
	State        int    `json:"state"`       // 唱歌作品制作结果;1:制作成功;2:制作失败
	ErrMsg       string `json:"err_msg"`     // 唱歌作品制作失败时携带的原因
	CreateAt     int64  `json:"create_at"`   // 唱歌作品制作时间
}

// MakeDancingPayload 跳舞作品制作消息载荷
type MakeDancingPayload struct {
	DancingID    int64  `json:"dancing_id"`    // 跳舞作品id
	DancingCover string `json:"dancing_cover"` // 跳舞作品封面图片地址
	State        int    `json:"state"`         // 跳舞作品制作结果;1:制作成功;2:制作失败
	ErrMsg       string `json:"err_msg"`       // 跳舞作品制作失败时携带的原因
	CreateAt     int64  `json:"create_at"`     // 跳舞作品制作时间
}
