package controller

import (
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TemplateAIController struct {
	templateAIRepo repo.TemplateAIRepo
}

func NewTemplateAIController(templateAIRepo repo.TemplateAIRepo) *TemplateAIController {
	return &TemplateAIController{templateAIRepo: templateAIRepo}
}

// Create 创建AI模板
func (c *TemplateAIController) Create(ctx *gin.Context) {
	var template dto.TemplateAIDTO
	if err := ctx.ShouldBindJSON(&template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.BadRequest), response.WithNoSLSLog)
		return
	}
	if err := c.templateAIRepo.Create(ctx, &template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// Update 更新AI模板
func (c *TemplateAIController) Update(ctx *gin.Context) {
	var template dto.TemplateAIDTO
	if err := ctx.ShouldBindJSON(&template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.BadRequest), response.WithNoSLSLog)
		return
	}
	if err := c.templateAIRepo.Update(ctx, &template); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// Delete 删除AI模板
func (c *TemplateAIController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "无效的ID"), response.WithNoSLSLog)
		return
	}
	if err := c.templateAIRepo.Delete(ctx, id); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithNoSLSLog)
}

// GetByID 根据ID获取AI模板
func (c *TemplateAIController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, "无效的ID"), response.WithNoSLSLog)
		return
	}
	template, err := c.templateAIRepo.GetByID(ctx, id)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, template, nil, response.WithNoSLSLog)
}

// ListByQuery 根据条件查询AI模板列表和总数
func (c *TemplateAIController) ListByQuery(ctx *gin.Context) {
	var query dto.TemplateAIQueryDTO
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.BadRequest), response.WithNoSLSLog)
		return
	}

	// 获取列表数据
	templates, err := c.templateAIRepo.ListByQuery(ctx, &query)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	// 获取总数
	count, err := c.templateAIRepo.Count(ctx, &query)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}

	var resp []*dto.TemplateDto
	for _, template := range templates {
		resp = append(resp, &dto.TemplateDto{
			ID:            template.ID,
			Name:          template.Name,
			Description:   template.Description,
			CategoryID:    template.CategoryID,
			CreateAt:      template.CreateAt,
			UpdatedAt:     template.UpdatedAt,
			DiamondCost:   template.DiamondCost,
			VideoCoverURL: template.VideoCoverURL,
			CoverURL:      template.CoverURL,
			Status:        template.Status,
			SortOrder:     template.SortOrder,
			MaxVersionInt: template.MaxVersionInt,
		})
	}

	// 构造返回数据
	result := map[string]interface{}{
		"list":  resp,
		"total": count,
	}

	response.Response(ctx, nil, result, nil, response.WithNoSLSLog)
}

// ListAll 查询所有AI模板
func (c *TemplateAIController) ListAll(ctx *gin.Context) {
	templates, err := c.templateAIRepo.ListAll(ctx)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleErrorWithCause(err, response.InternalServerError), response.WithNoSLSLog)
		return
	}
	response.Response(ctx, nil, templates, nil, response.WithNoSLSLog)
}
