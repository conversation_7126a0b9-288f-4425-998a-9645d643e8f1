package tencentcloud

import (
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	facefusion "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/facefusion/v20220927"
)

type FaceFusionClient struct {
	client *facefusion.Client
}

func NewFaceFusionClient(secretId, secretKey, region string) (*FaceFusionClient, error) {
	credential := common.NewCredential(secretId, secretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "facefusion.tencentcloudapi.com"
	client, err := facefusion.NewClient(credential, region, cpf)
	if err != nil {
		return nil, err
	}
	return &FaceFusionClient{client: client}, nil
}

func (c *FaceFusionClient) FaceFusion(projectId, modelId, rspImgType, image string, mergeInfos []*facefusion.MergeInfo) (*facefusion.FuseFaceResponse, error) {
	request := facefusion.NewFuseFaceRequest()
	request.ProjectId = &projectId
	request.ModelId = &modelId
	request.RspImgType = &rspImgType
	// request.Image = &image
	request.MergeInfos = mergeInfos

	return c.client.FuseFace(request)
}
