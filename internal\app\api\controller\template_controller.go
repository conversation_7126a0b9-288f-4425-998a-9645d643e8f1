package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"github.com/gin-gonic/gin"
	"strconv"
)

type TemplateController struct {
	log         *logger.Logger
	templateBiz *biz.TemplateBiz
}

func NewTemplateController(
	bootStrap *component.BootStrap,
	templateBiz *biz.TemplateBiz,
) *TemplateController {
	return &TemplateController{
		log:         bootStrap.Log,
		templateBiz: templateBiz,
	}
}

func (t *TemplateController) GetTemplateList(ctx *gin.Context) {
	templateCategoryId := ctx.Param("id")
	if templateCategoryId == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("category id is required"), response.WithSLSLog)
		return
	}

	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("version is required"), response.WithSLSLog)
		return
	}

	categoryId, err := strconv.ParseInt(templateCategoryId, 10, 64)
	if err != nil || categoryId <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid category id"), response.WithSLSLog)
		return
	}

	maxVersionInt := utils.VersionToVersionInt(version)
	if maxVersionInt <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid version"), response.WithSLSLog)
		return
	}

	data, err := t.templateBiz.GetTemplateList(ctx, categoryId, maxVersionInt)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}

	if len(data) == 0 {
		t.log.Warning("No templates found for category ID: %d", categoryId)
		data = make([]*dto.TemplateAIDTO, 0)
	}

	var resp []*dto.TemplateDto
	for _, template := range data {
		resp = append(resp, &dto.TemplateDto{
			ID:            template.ID,
			Name:          template.Name,
			CoverURL:      template.CoverURL,
			VideoCoverURL: template.VideoCoverURL,
			Status:        template.Status,
			SortOrder:     template.SortOrder,
			MaxVersionInt: template.MaxVersionInt,
			CategoryID:    template.CategoryID,
			Description:   template.Description,
			CreateAt:      template.CreateAt,
			UpdatedAt:     template.UpdatedAt,
			DiamondCost:   template.DiamondCost,
		})
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}

func (t *TemplateController) GetTemplateDetail(ctx *gin.Context) {
	templateId := ctx.Param("id")
	if templateId == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("template id is required"), response.WithSLSLog)
		return
	}

	id, err := strconv.ParseUint(templateId, 10, 64)
	if err != nil || id <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("invalid template id"), response.WithSLSLog)
		return
	}

	data, err := t.templateBiz.GetTemplateDetail(ctx, id)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(err.Error()), response.WithSLSLog)
		return
	}

	var resp *dto.TemplateDto
	if data != nil {
		resp = &dto.TemplateDto{
			ID:            data.ID,
			Name:          data.Name,
			CoverURL:      data.CoverURL,
			VideoCoverURL: data.VideoCoverURL,
			Status:        data.Status,
			SortOrder:     data.SortOrder,
			MaxVersionInt: data.MaxVersionInt,
			CategoryID:    data.CategoryID,
			Description:   data.Description,
			CreateAt:      data.CreateAt,
			UpdatedAt:     data.UpdatedAt,
			DiamondCost:   data.DiamondCost,
		}
	} else {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("模板内容为空"), response.WithSLSLog)
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}
