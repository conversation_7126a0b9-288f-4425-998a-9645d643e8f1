package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/pkg/comfyui"
	"chongli/internal/pkg/volcengine"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"chongli/pkg/utils"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type PicTaskProcessors struct {
	bootstrap   *component.BootStrap
	taskService *service.TaskService
	log         *logger.Logger
}

// NewPicTaskProcessors 创建图片任务处理器实例
func NewPicTaskProcessors(
	bootstrap *component.BootStrap,
	taskService *service.TaskService,
) *PicTaskProcessors {
	return &PicTaskProcessors{
		bootstrap:   bootstrap,
		taskService: taskService,
		log:         bootstrap.Log,
	}
}

// 第一步
func (s *PicTaskProcessors) AiCompound() {
	ctx := context.Background()

	// 1. 创建ComfyUI客户端进行队列检查
	comfyClient := comfyui.NewClient(s.bootstrap.Config.AiDomain)

	// 2. 检查队列状态
	queueStatus, err := comfyClient.GetPromptQueue(ctx)
	if err != nil {
		s.log.Error("检查队列状态时出错: %v", err)
		// 即使检查队列失败，也继续执行，不中断流程
	} else {
		// 增加对queueStatus为nil的判断
		if queueStatus == nil {
			s.log.Info("队列状态返回为空，继续执行")
		} else if queueStatus.ExecInfo.QueueRemaining >= 2 {
			s.log.Error("当前有%d个任务在等待处理，队列繁忙，跳过此次执行", queueStatus.ExecInfo.QueueRemaining)
			return
		}
	}

	// 第一个事务：获取待处理的步骤
	var step *dto.TaskStepDTO

	step, err = s.taskService.GetPendingTaskStep(ctx, "ai_compound", nil)
	if err != nil {
		s.log.Error("获取待处理的步骤失败: %v", err)
		return
	}

	// 验证步骤参数
	params, err := s.validateAiCompoundParams(step)
	if err != nil {
		s.handleStepError(ctx, step.ID, err.Error())
		return
	}

	err = s.uploadImageToComfyUI(ctx, comfyClient, params.ReferenceImage)
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("上传参考图片失败: %v", err))
		return
	}
	// 执行AI图片合成处理
	taskID, err := s.processAiCompound(ctx, comfyClient, params)
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("AI图片合成处理失败: %v", err))
		return
	}

	// 将task_id写入result并更新状态为waiting_result
	// 将map序列化为JSON字符串，再存入数据库
	resultJSON, err := json.Marshal(map[string]interface{}{
		"task_id": taskID,
	})
	if err != nil {
		s.log.Error("序列化结果数据失败: %v", err)
		s.handleStepError(ctx, step.ID, fmt.Sprintf("序列化结果数据失败: %v", err))
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step.ID, model.StepStatusWaitingResult, resultJSON)
	if err != nil {
		s.log.Error("更新步骤状态失败: %v", err)
		s.handleStepError(ctx, step.ID, fmt.Sprintf("更新步骤状态失败: %v", err))
		return
	}

	s.log.Info("AI图片合成任务已提交，task_id: %s", taskID)
}

// ChangeStyle 处理AI图片合成结果
func (s *PicTaskProcessors) ChangeStyle() {
	ctx := context.Background()

	// 1.获取状态是waiting_result，步骤名字是ai_compound的任务
	step, err := s.getWaitingResultStep(ctx, "ai_compound")
	if err != nil {
		s.log.Error("获取waiting_result状态的ai_compound任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的ai_compound任务")
		return
	}

	// 2. 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("解析task_id失败: %v", err))
		return
	}
	comfyClient := comfyui.NewClient(s.bootstrap.Config.AiDomain)
	// 3.调用internal/pkg/comfyui/comfyui.go函数History 拿到图片地址
	historyResp, err := comfyClient.GetHistoryWithStatus(ctx, taskID)
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("获取ComfyUI历史记录失败: %v", err))
		return
	}

	// 检查任务状态
	if historyResp.Status == "running" {
		s.log.Info("任务 %s 仍在运行中，跳过此次处理", taskID)
		return
	}

	if historyResp.Status == "failed" {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("ComfyUI任务失败: %s", historyResp.Message))
		return
	}

	if historyResp.Status != "completed" || historyResp.ImageURL == "" {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("ComfyUI任务状态异常: %s", historyResp.Status))
		return
	}

	// 4.图片地址下载到本地，然后上传到qiniu
	qiniuURL, err := s.downloadAndUploadToQiniu(utils.EnsureHttpsPrefix(historyResp.ImageURL))
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("下载并上传图片失败: %v", err))
		return
	}

	// 更新当前步骤为完成
	resultJSON, err := json.Marshal(map[string]any{
		"image_url": utils.EnsureHttpsPrefix(qiniuURL),
		"task_id":   taskID,
	})
	if err != nil {
		s.log.Error("序列化结果数据失败: %v", err)
		s.handleStepError(ctx, step.ID, fmt.Sprintf("序列化结果数据失败: %v", err))
		return
	}
	err = s.taskService.UpdateStepStatusAndResult(ctx, step.ID, model.StepStatusDone, resultJSON)
	if err != nil {
		s.log.Error("更新步骤状态失败: %v", err)
		s.handleStepError(ctx, step.ID, fmt.Sprintf("更新步骤状态失败: %v", err))
		return
	}

	// 获取下一步
	nextSteps, err := s.taskService.GetStepWithTask(ctx, &dto.GetStepWithTaskQuery{
		Step: &dto.TaskStepDTO{
			StepIndex: step.StepIndex + 1,
			TaskID:    step.TaskID,
		},
	})
	if err != nil {
		s.log.Error("获取下一步失败: %v", err)
		s.handleStepError(ctx, step.ID, fmt.Sprintf("获取下一步失败: %v", err))
		return
	}
	if nextSteps == nil {
		s.log.Info("任务 %d 已完成", step.TaskID)
		return
	}
	if len(nextSteps) == 0 {
		s.log.Error("获取下一步失败: 获取到的下一步是0个")
		return
	}
	nextStep := nextSteps[0]
	// 检查下一步参数
	if nextStep.Params == nil {
		s.handleStepError(ctx, nextStep.ID, "下一步参数为空")
		return
	}
	styleDesc, ok := nextStep.Params["style_desc"]
	if !ok {
		s.handleStepError(ctx, nextStep.ID, "下一步参数中缺少style_desc")
		return
	}
	styleDescStr, ok := styleDesc.(string)
	if !ok {
		s.handleStepError(ctx, nextStep.ID, "style_desc参数类型错误，应为string")
		return
	}
	if styleDescStr == "" {
		s.handleStepError(ctx, nextStep.ID, "style_desc参数不能为空")
		return
	}

	// 提交火山引擎任务
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})
	volcReq := &volcengine.CVSync2AsyncSubmitTaskRequest{
		ImageUrls: []string{utils.EnsureHttpsPrefix(qiniuURL)},
		Prompt:    styleDescStr,
	}
	volcResp, err := volcClient.CVSync2AsyncSubmitTask(volcReq)
	if err != nil {
		s.handleStepError(ctx, nextStep.ID, fmt.Sprintf("提交火山引擎任务失败: %v", err))
		return
	}

	// 更新下一步骤状态
	nextResultJSON, err := json.Marshal(map[string]any{
		"task_id": volcResp.Data.TaskID,
	})
	if err != nil {
		s.log.Error("序列化下一步结果数据失败: %v", err)
		s.handleStepError(ctx, nextStep.ID, fmt.Sprintf("序列化下一步结果数据失败: %v", err))
		return
	}
	err = s.taskService.UpdateStepStatusAndResult(ctx, nextStep.ID, model.StepStatusWaitingResult, nextResultJSON)
	if err != nil {
		s.log.Error("更新下一步骤状态失败: %v", err)
		s.handleStepError(ctx, nextStep.ID, fmt.Sprintf("更新下一步骤状态失败: %v", err))
		return
	}

	s.log.Info("AI图片风格转换任务已提交，task_id: %s", volcResp.Data.TaskID)
}
func (s *PicTaskProcessors) ChangeFace() {
	ctx := context.Background()

	// 1.获取状态是waiting_result，步骤名字是ai_compound的任务
	step, err := s.getWaitingResultStep(ctx, "change_style")
	if err != nil {
		s.log.Error("获取waiting_result状态的change_style任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的change_style任务")
		return
	}

	// 2.解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("解析task_id失败: %v", err))
		return
	}
	// 提交火山引擎任务
	volcClient := volcengine.NewClient(&volcengine.Config{
		AccessKey: s.bootstrap.Config.VolcengineAccessKey,
		SecretKey: s.bootstrap.Config.VolcengineSecretKey,
	})
	volcReq := &volcengine.CVSync2AsyncGetResultRequest{
		ReqKey: "seededit_v3.0",
		TaskID: taskID,
	}
	volcResp, err := volcClient.CVSync2AsyncGetResult(volcReq)
	if err != nil {
		s.handleStepError(ctx, step.ID, fmt.Sprintf("提交火山引擎任务失败: %v", err))
		return
	}
	if volcResp != nil && len(volcResp.Data.BinaryDataBase64) > 0 {

		// 上传到七牛云
		qiniuURL, err := s.downloadAndUploadToQiniu(volcResp.Data.ImageUrls[0])
		if err != nil {
			s.handleStepError(ctx, step.ID, fmt.Sprintf("上传到七牛云失败: %v", err))
			return
		}

		resultJSON, err := json.Marshal(map[string]any{
			"image_url": utils.EnsureHttpsPrefix(qiniuURL),
			"task_id":   taskID,
		})
		if err != nil {
			s.log.Error("序列化结果数据失败: %v", err)
			s.handleStepError(ctx, step.ID, fmt.Sprintf("序列化结果数据失败: %v", err))
			return
		}
		err = s.taskService.UpdateStepStatusAndResult(ctx, step.ID, model.StepStatusDone, resultJSON)
		if err != nil {
			s.log.Error("更新步骤状态失败: %v", err)
			s.handleStepError(ctx, step.ID, fmt.Sprintf("更新步骤状态失败: %v", err))
			return
		}
		// 获取下一步
		nextSteps, err := s.taskService.GetStepWithTask(ctx, &dto.GetStepWithTaskQuery{
			Step: &dto.TaskStepDTO{
				StepIndex: step.StepIndex + 1,
				TaskID:    step.TaskID,
			},
		})
		if err != nil {
			s.log.Error("获取下一步失败: %v", err)
			s.handleStepError(ctx, step.ID, fmt.Sprintf("获取下一步失败: %v", err))
			return
		}
		if nextSteps == nil {
			s.log.Info("任务 %d 已完成", step.TaskID)
			return
		}
		if len(nextSteps) == 0 {
			s.log.Error("获取下一步失败: 获取到的下一步是0个")
			return
		}
		nextStep := nextSteps[0]
		// 检查下一步参数
		if nextStep.Params == nil {
			s.handleStepError(ctx, nextStep.ID, "下一步参数为空")
			return
		}
		persionPic, ok := nextStep.Params["persion_pic"]
		if !ok {
			s.handleStepError(ctx, nextStep.ID, "下一步参数中缺少persion_pic")
			return
		}
		persionPicStr, ok := persionPic.(string)
		if !ok {
			s.handleStepError(ctx, nextStep.ID, "persion_pic参数类型错误，应为string")
			return
		}
		fmt.Println("persionPicStr", persionPicStr)
	}
}

// downloadImage 下载图片到本地文件
func (s *PicTaskProcessors) downloadImage(url, filepath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存图片失败: %w", err)
	}

	return nil
}

// generateTempFilename 从 URL 生成临时文件名和路径
func (s *PicTaskProcessors) generateTempFilename(url string) (string, string) {
	// 从 URL 提取文件后缀
	ext := ".jpg" // 默认后缀
	if strings.Contains(url, ".") {
		extParts := strings.Split(url, ".")
		if len(extParts) > 1 {
			ext = "." + extParts[len(extParts)-1]
			// 如果后缀包含参数，只取纯后缀部分
			if strings.Contains(ext, "?") {
				ext = ext[:strings.Index(ext, "?")]
			}
		}
	}

	// 使用 URL 计算 MD5 作为文件名
	hasher := md5.New()
	hasher.Write([]byte(url))
	hashBytes := hasher.Sum(nil)
	hashString := fmt.Sprintf("%x", hashBytes)

	// 创建临时文件路径
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, hashString+ext)

	return hashString + ext, tempFilePath
}

// uploadImageToComfyUI 上传图片到ComfyUI
func (s *PicTaskProcessors) uploadImageToComfyUI(ctx context.Context, client *comfyui.Client, fileurl string) error {
	// 生成文件名和临时文件路径
	filename, tempFilePath := s.generateTempFilename(fileurl)

	// 下载图片到本地
	err := s.downloadImage(fileurl, tempFilePath)
	if err != nil {
		return fmt.Errorf("下载图片失败: %w", err)
	}
	defer os.Remove(tempFilePath) // 函数执行完后删除临时文件

	// 打开文件准备上传
	file, err := os.Open(tempFilePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	_, err = client.UploadImage(ctx, file, filename)
	if err != nil {
		return fmt.Errorf("上传图片失败: %w", err)
	}

	return nil
}

// AiCompoundParams AI合成参数结构体
type AiCompoundParams struct {
	PetDescription         string `json:"petDescription"`
	BackgroundDescription  string `json:"backgroundDescription"`
	CompositionDescription string `json:"compositionDescription"`
	LoraSelection          string `json:"loraSelection"`
	ModelStrength          string `json:"modelStrength"`
	ReferenceImage         string `json:"referenceImage"`
	WorkflowUrl            string `json:"workflow_url"`
}

// validateAiCompoundParams 验证AI合成步骤参数
func (s *PicTaskProcessors) validateAiCompoundParams(step *dto.TaskStepDTO) (*AiCompoundParams, error) {
	// 获取pet_pic图片地址
	petPic, ok := step.Params["pet_pic"].(string)
	if !ok || petPic == "" {
		return nil, fmt.Errorf("pet_pic参数缺失或格式错误")
	}

	// 获取pet_desc
	petDesc, ok := step.Params["pet_desc"].(string)
	if !ok || petDesc == "" {
		return nil, fmt.Errorf("pet_desc参数缺失或格式错误")
	}

	// 获取background_desc
	backgroundDesc, ok := step.Params["background_desc"].(string)
	if !ok || backgroundDesc == "" {
		return nil, fmt.Errorf("background_desc参数缺失或格式错误")
	}

	// 获取composition_desc
	compositionDesc, ok := step.Params["composition_desc"].(string)
	if !ok || compositionDesc == "" {
		return nil, fmt.Errorf("composition_desc参数缺失或格式错误")
	}

	// 获取style (对应lora_selection)
	style, ok := step.Params["style"].(string)
	if !ok || style == "" {
		return nil, fmt.Errorf("style参数缺失或格式错误")
	}
	workflow_url, ok := step.Params["workflow_url"].(string)
	if !ok || workflow_url == "" {
		return nil, fmt.Errorf("workflow_url参数缺失或格式错误")
	}
	// 获取strength (对应model_strength)
	var strengthStr string
	if strength, ok := step.Params["strength"].(float64); ok {
		strengthStr = strconv.FormatFloat(strength, 'f', -1, 64)
	} else if strength, ok := step.Params["strength"].(string); ok {
		strengthStr = strength
	} else {
		strengthStr = "1" // 默认值
	}

	return &AiCompoundParams{
		PetDescription:         petDesc,
		BackgroundDescription:  backgroundDesc,
		CompositionDescription: compositionDesc,
		LoraSelection:          style,
		ModelStrength:          strengthStr,
		ReferenceImage:         petPic,
		WorkflowUrl:            workflow_url,
	}, nil
}

// processAiCompound 实现AI图片合成的核心逻辑（基于Python版本）
func (s *PicTaskProcessors) processAiCompound(ctx context.Context, comfyClient *comfyui.Client, params *AiCompoundParams) (string, error) {

	workflowPath, err := s.downloadWorkflowWithCache(params.WorkflowUrl)
	if err != nil {
		return "", fmt.Errorf("下载workflow文件失败: %w", err)
	}

	// 生成唯一客户端ID
	clientID := fmt.Sprintf("go-client-%d", time.Now().UnixNano())

	// 读取pet.json模板文件
	promptData, err := s.loadPetJsonTemplate(workflowPath)
	if err != nil {
		return "", fmt.Errorf("读取pet.json模板失败: %w", err)
	}

	// 替换模板中的参数
	err = s.replacePromptParams(promptData, params)
	if err != nil {
		return "", fmt.Errorf("替换模板参数失败: %w", err)
	}

	// 设置随机seed值
	s.setRandomSeeds(promptData)

	// 发送prompt请求到ComfyUI
	promptResp, err := comfyClient.Prompt(ctx, clientID, promptData)
	if err != nil {
		return "", fmt.Errorf("发送prompt请求失败: %w", err)
	}

	// 返回prompt_id作为task_id
	return promptResp.PromptID, nil
}

// loadPetJsonTemplate 读取pet.json模板文件
func (s *PicTaskProcessors) loadPetJsonTemplate(workflowPath string) (map[string]interface{}, error) {
	// 读取pet.json文件
	data, err := os.ReadFile(workflowPath)
	if err != nil {
		return nil, fmt.Errorf("读取pet.json文件失败: %w", err)
	}

	var promptData map[string]interface{}
	if err := json.Unmarshal(data, &promptData); err != nil {
		return nil, fmt.Errorf("解析pet.json文件失败: %w", err)
	}

	return promptData, nil
}

// replacePromptParams 替换模板中的参数
func (s *PicTaskProcessors) replacePromptParams(promptData map[string]interface{}, params *AiCompoundParams) error {
	// 替换宠物描述 (节点 "28")
	if err := s.setNodeParam(promptData, "28", "text_b", params.PetDescription); err != nil {
		return fmt.Errorf("设置宠物描述失败: %w", err)
	}

	// 替换背景描述 (节点 "30")
	if err := s.setNodeParam(promptData, "30", "text_b", params.BackgroundDescription); err != nil {
		return fmt.Errorf("设置背景描述失败: %w", err)
	}

	// 替换构图描述 (节点 "66")
	if err := s.setNodeParam(promptData, "66", "text_b", params.CompositionDescription); err != nil {
		return fmt.Errorf("设置构图描述失败: %w", err)
	}
	filename, _ := s.generateTempFilename(params.ReferenceImage)
	// 替换参考图片 (节点 "14")
	if err := s.setNodeParam(promptData, "14", "image", filename); err != nil {
		return fmt.Errorf("设置参考图片失败: %w", err)
	}

	// 替换LoRA选择 (节点 "31")
	if err := s.setNodeParam(promptData, "31", "lora_name", params.LoraSelection); err != nil {
		return fmt.Errorf("设置LoRA选择失败: %w", err)
	}

	// 替换模型强度 (节点 "63")
	strength, err := strconv.ParseFloat(params.ModelStrength, 64)
	if err != nil {
		strength = 1.0 // 默认值
	}
	if err := s.setNodeParam(promptData, "63", "strength_model", strength); err != nil {
		return fmt.Errorf("设置模型强度失败: %w", err)
	}

	return nil
}

// setNodeParam 设置节点参数的辅助函数
func (s *PicTaskProcessors) setNodeParam(promptData map[string]interface{}, nodeID, paramKey string, value interface{}) error {
	node, exists := promptData[nodeID]
	if !exists {
		return fmt.Errorf("pet.json中没有找到节点%s", nodeID)
	}

	nodeMap, ok := node.(map[string]interface{})
	if !ok {
		return fmt.Errorf("节点%s格式错误", nodeID)
	}

	inputs, exists := nodeMap["inputs"]
	if !exists {
		return fmt.Errorf("节点%s中没有找到inputs", nodeID)
	}

	inputsMap, ok := inputs.(map[string]interface{})
	if !ok {
		return fmt.Errorf("节点%s的inputs格式错误", nodeID)
	}

	inputsMap[paramKey] = value
	return nil
}

// setRandomSeeds 设置随机seed值
func (s *PicTaskProcessors) setRandomSeeds(promptData map[string]interface{}) {
	// 7, 11, 59这三个节点的seed值设置为随机的15位数字
	nodeIDs := []string{"7", "11", "59"}

	for _, nodeID := range nodeIDs {
		// 生成15位随机数字
		seed := s.generateRandomSeed()
		err := s.setNodeParam(promptData, nodeID, "seed", seed)
		if err != nil {
			s.log.Error("设置节点%s的seed失败: %v", nodeID, err)
		}
	}
}

// generateRandomSeed 生成15位随机数字
func (s *PicTaskProcessors) generateRandomSeed() int64 {
	// 生成100000000000000到999999999999999之间的随机数
	min := int64(100000000000000)
	max := int64(999999999999999)
	return min + (time.Now().UnixNano() % (max - min + 1))
}

// handleStepError 统一处理步骤失败的错误
func (s *PicTaskProcessors) handleStepError(ctx context.Context, stepID int64, errorMsg string) {
	err := s.taskService.CompleteStepAndCheckTask(ctx, &dto.CompleteStepRequest{
		StepID:   stepID,
		ErrorMsg: errorMsg,
	})
	if err != nil {
		s.log.Error("标记步骤失败时出错: %v", err)
	}
}

// downloadWorkflowWithCache 下载workflow文件并实现12小时缓存机制
func (s *PicTaskProcessors) downloadWorkflowWithCache(url string) (string, error) {
	// 生成缓存文件名：基于URL的MD5哈希
	h := md5.New()
	h.Write([]byte(url))
	cacheKey := fmt.Sprintf("%x", h.Sum(nil))

	// 创建缓存目录
	cacheDir := filepath.Join(os.TempDir(), "workflow_cache")
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		return "", fmt.Errorf("创建缓存目录失败: %w", err)
	}

	cacheFilePath := filepath.Join(cacheDir, cacheKey+".json")

	// 检查缓存文件是否存在且未过期
	if info, err := os.Stat(cacheFilePath); err == nil {
		// 检查文件是否在12小时内
		if time.Since(info.ModTime()) < 12*time.Hour {
			s.log.Info("使用缓存的workflow文件: %s", cacheFilePath)
			return cacheFilePath, nil
		} else {
			// 缓存过期，删除旧文件
			s.log.Info("workflow文件缓存已过期，重新下载: %s", url)
			os.Remove(cacheFilePath)
		}
	}

	// 下载workflow文件
	s.log.Info("下载workflow文件: %s", url)
	err := s.downloadImage(url, cacheFilePath)
	if err != nil {
		return "", fmt.Errorf("下载workflow文件失败: %w", err)
	}

	return cacheFilePath, nil
}

// cleanExpiredCache 清理过期的缓存文件（可选的辅助方法）
func (s *PicTaskProcessors) cleanExpiredCache() {
	cacheDir := filepath.Join(os.TempDir(), "workflow_cache")

	entries, err := os.ReadDir(cacheDir)
	if err != nil {
		return // 目录不存在或无法读取，忽略错误
	}

	now := time.Now()
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		info, err := entry.Info()
		if err != nil {
			continue
		}

		// 删除超过12小时的文件
		if now.Sub(info.ModTime()) > 12*time.Hour {
			filePath := filepath.Join(cacheDir, entry.Name())
			os.Remove(filePath)
			s.log.Info("清理过期缓存文件: %s", filePath)
		}
	}
}

// getWaitingResultStep 获取状态为waiting_result且步骤名为指定名称的任务步骤
func (s *PicTaskProcessors) getWaitingResultStep(ctx context.Context, stepName string) (*dto.TaskStepDTO, error) {
	limit := 1
	query := &dto.GetStepWithTaskQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusWaitingResult,
			StepName: stepName,
		},
		Limit: &limit,
	}

	stepDTOs, err := s.taskService.GetStepWithTask(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("获取waiting_result状态的步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, nil // 没有找到对应的步骤
	}

	return stepDTOs[0], nil
}

// extractTaskIDFromResult 从result字段中解析task_id
func (s *PicTaskProcessors) extractTaskIDFromResult(result dto.JSONMap) (string, error) {
	if result == nil {
		return "", fmt.Errorf("result字段为空")
	}

	taskID, ok := result["task_id"].(string)
	if !ok {
		return "", fmt.Errorf("result中没有找到task_id或类型错误")
	}

	if taskID == "" {
		return "", fmt.Errorf("task_id为空")
	}

	return taskID, nil
}

// downloadAndUploadToQiniu 下载图片到本地并上传到七牛云
func (s *PicTaskProcessors) downloadAndUploadToQiniu(imageURL string) (string, error) {
	// 生成临时文件名和路径
	filename, tempFilePath := s.generateTempFilename(imageURL)

	// 下载图片到本地
	err := s.downloadImage(imageURL, tempFilePath)
	if err != nil {
		return "", fmt.Errorf("下载图片失败: %w", err)
	}
	defer os.Remove(tempFilePath) // 函数执行完后删除临时文件

	// 上传到七牛云
	qiniuURL, err := s.uploadLocalFileToQiniu(tempFilePath, filename)
	if err != nil {
		return "", fmt.Errorf("上传到七牛云失败: %w", err)
	}

	return qiniuURL, nil
}

// uploadLocalFileToQiniu 上传本地文件到七牛云
func (s *PicTaskProcessors) uploadLocalFileToQiniu(localFilePath, filename string) (string, error) {
	// 构建七牛云存储路径，使用 chongli-ai-tmp 作为根路径（用于临时文件，后期会按时间删除）
	qiniuPath := fmt.Sprintf("chongli-ai-tmp/%s/%s", time.Now().Format("2006/01/02"), filename)

	// 使用新创建的 qiniu.UploadLocalFile 方法
	return qiniu.UploadLocalFile(localFilePath, qiniuPath)
}
