package volcengine

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/volcengine/volc-sdk-golang/service/visual"
)

// Config 火山引擎配置
type Config struct {
	AccessKey string
	SecretKey string
	Region    string
	Host      string
}

// Client 火山引擎客户端
type Client struct {
	config *Config
}

// NewClient 创建新的火山引擎客户端
func NewClient(config *Config) *Client {
	client := &Client{
		config: config,
	}

	// 设置访问密钥
	visual.DefaultInstance.Client.SetAccessKey(config.AccessKey)
	visual.DefaultInstance.Client.SetSecretKey(config.SecretKey)

	// 可选设置区域和主机
	if config.Region != "" {
		visual.DefaultInstance.SetRegion(config.Region)
	}
	if config.Host != "" {
		visual.DefaultInstance.SetHost(config.Host)
	}

	return client
}

// CVSync2AsyncSubmitTaskRequest 提交异步任务请求参数
type CVSync2AsyncSubmitTaskRequest struct {
	ReqKey           string   `json:"req_key"`
	BinaryDataBase64 string   `json:"binary_data_base64"`
	Prompt           string   `json:"prompt"`
	ImageUrls        []string `json:"image_urls"`
}
type CVSync2AsyncGetResultResponse struct {
	Status      int    `json:"status"`
	Code        int    `json:"code"`
	Message     string `json:"message"`
	RequestID   string `json:"request_id"`
	TimeElapsed string `json:"time_elapsed"`
	Data        struct {
		BinaryDataBase64 []string `json:"binary_data_base64"`
		ImageUrls        []string `json:"image_urls"`
		RespData         string   `json:"resp_data"`
		Status           string   `json:"status"`
	} `json:"data"`
	Error string `json:"error,omitempty"`
}

// CVSync2AsyncSubmitTaskResponse 提交异步任务响应
type CVSync2AsyncSubmitTaskResponse struct {
	Status      int    `json:"status"`
	Code        int    `json:"code"`
	Message     string `json:"message"`
	RequestID   string `json:"request_id"`
	TimeElapsed string `json:"time_elapsed"`
	Data        struct {
		TaskID string `json:"task_id"`
	} `json:"data"`
	Error string `json:"error,omitempty"`
}

// CVSync2AsyncGetResultRequest 获取异步任务结果请求参数
type CVSync2AsyncGetResultRequest struct {
	ReqKey string `json:"req_key"`
	TaskID string `json:"task_id"`
}

// CVSync2AsyncSubmitTask 提交异步任务
func (c *Client) CVSync2AsyncSubmitTask(req *CVSync2AsyncSubmitTaskRequest) (*CVSync2AsyncSubmitTaskResponse, error) {
	// 构建请求体
	reqBody := map[string]any{
		"req_key":    "seededit_v3.0",
		"prompt":     req.Prompt,
		"image_urls": req.ImageUrls,
	}

	var (
		resp   map[string]interface{}
		status int
		err    error
	)

	for i := 0; i < 4; i++ {
		// 调用火山引擎SDK
		resp, status, err = visual.DefaultInstance.CVSync2AsyncSubmitTask(reqBody)
		if err != nil {
			return nil, fmt.Errorf("call CVSync2AsyncSubmitTask failed: %w", err)
		}

		if status != 429 {
			break
		}
		time.Sleep(10 * time.Second)
	}

	// 构建响应
	result := &CVSync2AsyncSubmitTaskResponse{}
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}
	if err := json.Unmarshal(respBytes, result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	result.Status = status
	// 如果状态码不是200，将其作为错误处理
	if status != 200 {
		// 尝试从响应中提取错误信息
		if errMsg, exists := resp["error"]; exists {
			result.Error = fmt.Sprintf("%v", errMsg)
		}
		if result.Error == "" {
			result.Error = fmt.Sprintf("HTTP status: %d", status)
		}
	}

	return result, nil
}

// CVSync2AsyncGetResult 获取异步任务结果
func (c *Client) CVSync2AsyncGetResult(req *CVSync2AsyncGetResultRequest) (*CVSync2AsyncGetResultResponse, error) {
	reqKey := "seededit_v3.0"
	if len(req.ReqKey) != 0 {
		reqKey = req.ReqKey
	}
	// 构建请求体
	reqBody := map[string]interface{}{
		"req_key":  reqKey,
		"task_id":  req.TaskID,
		"req_json": `{"return_url":true}`,
	}

	// 调用火山引擎SDK
	resp, status, err := visual.DefaultInstance.CVSync2AsyncGetResult(reqBody)
	if err != nil {
		return nil, fmt.Errorf("call CVSync2AsyncGetResult failed: %w", err)
	}
	// 构建响应
	result := &CVSync2AsyncGetResultResponse{}
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}
	if err := json.Unmarshal(respBytes, result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	result.Status = status
	if status != 200 {
		return nil, fmt.Errorf("HTTP status: %d:%v", status, result.Message)
	}
	if result.Code != 1000 {
		return nil, fmt.Errorf("volcengine code: %d:%v", result.Code, result.Message)
	}
	if result.Data.Status == "in_queue" {
		return nil, fmt.Errorf("volcengine status: %s", result.Data.Status)
	}
	if result.Data.Status == "generating" {
		return nil, nil
	}
	if result.Data.Status == "done" {
		return result, nil
	}
	if result.Data.Status == "not_found" {
		return nil, fmt.Errorf("volcengine status: %s", result.Data.Status)
	}
	if result.Data.Status == "expired" {
		return nil, fmt.Errorf("volcengine status: %s", result.Data.Status)
	}
	return result, nil
}

// AddSubmitTaskParam 为提交任务添加额外参数的辅助方法
func (c *Client) CVSync2AsyncSubmitTaskWithParams(reqKey string, extraParams map[string]interface{}) (*CVSync2AsyncSubmitTaskResponse, error) {
	// 构建请求体
	reqBody := map[string]interface{}{
		"req_key": reqKey,
	}

	// 添加额外参数
	for key, value := range extraParams {
		reqBody[key] = value
	}

	// 调用火山引擎SDK
	resp, status, err := visual.DefaultInstance.CVSync2AsyncSubmitTask(reqBody)
	if err != nil {
		return nil, fmt.Errorf("call CVSync2AsyncSubmitTask failed: %w", err)
	}

	// 如果状态码不是200，将其作为错误处理
	if status != 200 {
		// 尝试从响应中提取错误信息
		if errMsg, exists := resp["error"]; exists {
			return nil, fmt.Errorf("%v", errMsg)
		}
		return nil, fmt.Errorf("HTTP status: %d", status)
	}
	result := &CVSync2AsyncSubmitTaskResponse{}
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}
	if err := json.Unmarshal(respBytes, result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	return result, nil
}

// FormatResponse 格式化响应为JSON字符串（用于调试）
func FormatResponse(resp interface{}) string {
	b, err := json.Marshal(resp)
	if err != nil {
		return fmt.Sprintf("marshal error: %v", err)
	}
	return string(b)
}
