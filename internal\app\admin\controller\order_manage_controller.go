package controller

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"github.com/gin-gonic/gin"
	"strconv"
)

type PayOrderManageController struct {
	log              *logger.Logger
	tx               driver.ITransaction
	payOrderRepo     repo.PayOrderRepo
	payNotifyService *service.PayNotifyService
}

func NewPayOrderManageController(
	bootStrap *component.BootStrap,
	payOrderRepo repo.PayOrderRepo,
	payNotifyService *service.PayNotifyService,
) *PayOrderManageController {
	return &PayOrderManageController{
		log:              bootStrap.Log,
		tx:               bootStrap.Tx,
		payOrderRepo:     payOrderRepo,
		payNotifyService: payNotifyService,
	}
}

// OrderList 获取订单列表
func (c *PayOrderManageController) OrderList(ctx *gin.Context) {
	var req dto.GetPayOrderListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	orders, total, err := c.payOrderRepo.GetPayOrderList(&req)
	if err != nil {
		c.log.Error("获取订单列表失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, &dto.GetPayOrderListResponse{
		Total: total,
		List:  orders,
	}, nil, response.WithSLSLog)
}

// OrderDetail 获取订单详情
func (c *PayOrderManageController) OrderDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(response.BadRequest), response.WithSLSLog)
		return
	}

	order, err := c.payOrderRepo.GetPayOrderByID(id)
	if err != nil {
		c.log.Error("获取订单详情失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	if order == nil || order.ID == 0 {
		response.Response(ctx, nil, nil, errpkg.NewLowError("订单不存在"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, &dto.PayOrderDetailResponse{
		PayOrderDto: order,
	}, nil, response.WithSLSLog)
}

func (c *PayOrderManageController) OrderRefund(ctx *gin.Context) {
	var req dto.PayNotifyDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, errpkg.NewMiddleError("退款绑定参数错误"), nil, response.WithSLSLog)
		return
	}

	if *req.NotifyType != "REFUND" {
		response.Response(ctx, nil, errpkg.NewHighError("非退款请求"), nil, response.WithSLSLog)
		return
	}

	err := c.payNotifyService.PayNotify(&req)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewHighError(err.Error()), response.WithSLSLog)
	}

	response.Response(ctx, nil, "退款已处理", nil, response.WithSLSLog)
}
