package repo

import (
	"context"
	"time"
)

// RedisRepo Redis 数据仓库接口
type RedisRepo interface {
	// Get 从 Redis 获取值
	Get(ctx context.Context, key string) (string, error)

	// Set 设置值到 Redis
	Set(ctx context.Context, key string, value string, expiration time.Duration) error

	// Del 从 Redis 删除值
	Del(ctx context.Context, key string) error

	// Exists 检查 key 是否存在
	Exists(ctx context.Context, key string) (bool, error)
}
