package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"

	"gorm.io/gorm"
)

type versionDao struct {
	db *gorm.DB
}

// modelToDto 将 model 转换为 dto
func modelToDto(m *model.VersionModel) *dto.VersionDto {
	if m == nil {
		return nil
	}
	return &dto.VersionDto{
		ID:          m.ID,
		Version:     m.Version,
		Desc:        m.Desc,
		IsForce:     model.StatusFlag(m.IsForce),
		Channel:     m.Channel,
		CreateAt:    m.CreateAt,
		IsDelete:    model.StatusFlag(m.IsDelete),
		DownloadUrl: m.DownloadUrl,
		VersionInt:  m.VersionInt,
		UpdateAt:    m.UpdateAt,
		IsRelease:   model.StatusFlag(m.IsRelease),
	}
}

// dtoToModel 将 dto 转换为 model
func dtoToModel(d *dto.VersionDto) *model.VersionModel {
	if d == nil {
		return nil
	}
	return &model.VersionModel{
		ID:          d.ID,
		Version:     d.Version,
		Desc:        d.Desc,
		IsForce:     int8(d.IsForce),
		Channel:     d.Channel,
		CreateAt:    d.CreateAt,
		IsDelete:    int8(d.IsDelete),
		DownloadUrl: d.DownloadUrl,
		VersionInt:  d.VersionInt,
		UpdateAt:    d.UpdateAt,
		IsRelease:   int8(d.IsRelease),
	}
}

// modelsToDto 将 model 数组转换为 dto 数组
func modelsToDto(models []*model.VersionModel) []*dto.VersionDto {
	if models == nil {
		return nil
	}
	dtos := make([]*dto.VersionDto, len(models))
	for i, m := range models {
		dtos[i] = modelToDto(m)
	}
	return dtos
}

// dtosToModel 将 dto 数组转换为 model 数组
func dtosToModel(dtos *dto.VersionDto) []*model.VersionModel {
	if dtos == nil {
		return nil
	}
	models := make([]*model.VersionModel, 1)
	models[0] = dtoToModel(dtos)
	return models
}

func NewVersionRepo(bootStrap *component.BootStrap) repo.VersionRepo {
	return &versionDao{db: bootStrap.Driver.GetMysqlDb()}
}

func (d *versionDao) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 {
		return tx[0]
	}
	return d.db
}

// buildQuery 构建查询条件
func (d *versionDao) buildQuery(db *gorm.DB, request *dto.VersionQueryRequest) *gorm.DB {
	if request == nil {
		return db
	}

	// ID筛选
	if request.ID > 0 {
		db = db.Where("id = ?", request.ID)
	}

	// Version筛选
	if request.Version != "" {
		db = db.Where("version LIKE ?", "%"+request.Version+"%")
	}

	// IsForce筛选
	if request.IsForce != 0 {
		db = db.Where("is_force = ?", int8(request.IsForce))
	}

	// Channel筛选
	if request.Channel != "" {
		db = db.Where("channel = ?", request.Channel)
	}

	// VersionInt筛选
	if request.VersionInt > 0 {
		db = db.Where("version_int = ?", request.VersionInt)
	}

	// IsDelete筛选
	if request.IsDelete != 0 {
		db = db.Where("is_delete = ?", int8(request.IsDelete))
	}

	// IsRelease筛选
	if request.IsRelease != 0 {
		db = db.Where("is_release = ?", int8(request.IsRelease))
	}

	// 排序条件处理
	if request.OrderBy != "" {
		db = db.Order(request.OrderBy)
	} else {
		// 默认按创建时间倒序
		db = db.Order("create_at desc")
	}

	return db
}

func (d *versionDao) SelectLatestVersion(channel string, tx ...*gorm.DB) (*dto.VersionDto, error) {
	var version *model.VersionModel
	if err := d.getDb(tx).Model(&model.VersionModel{}).
		Where("is_delete = ?", 0).
		Where("is_release = ?", 1).
		Where("channel = ?", channel).
		Order("version_int desc").
		Limit(1).Find(&version).Error; err != nil {
		logger.Log().Error("查询版本失败, err: %v channel: %v", err, channel)
		return nil, err
	}
	return modelToDto(version), nil
}

func (d *versionDao) Create(request *dto.VersionDto, tx ...*gorm.DB) error {
	versions := dtosToModel(request)
	if err := d.getDb(tx).Create(versions).Error; err != nil {
		logger.Log().Error("创建版本失败, err: %v versions: %+v", err, versions)
		return err
	}
	return nil
}

func (d *versionDao) Update(id int64, updates map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.VersionModel{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		logger.Log().Error("更新版本失败, err: %v id: %v updates: %+v", err, id, updates)
		return err
	}
	return nil
}

func (d *versionDao) Select(request *dto.VersionQueryRequest, tx ...*gorm.DB) (*dto.VersionDto, error) {
	var version *model.VersionModel
	db := d.buildQuery(d.getDb(tx).Model(&model.VersionModel{}), request)
	if err := db.Limit(1).Find(&version).Error; err != nil {
		logger.Log().Error("查询版本失败: %v, request: %+v", err, request)
		return nil, err
	}
	return modelToDto(version), nil
}

func (d *versionDao) List(request *dto.VersionQueryRequest, tx ...*gorm.DB) ([]*dto.VersionDto, error) {
	var versions []*model.VersionModel
	db := d.buildQuery(d.getDb(tx).Model(&model.VersionModel{}), request)
	if err := db.Find(&versions).Error; err != nil {
		logger.Log().Error("查询版本失败: %v, request: %+v", err, request)
		return nil, err
	}
	return modelsToDto(versions), nil
}

func (d *versionDao) PageQuery(request *dto.VersionPageQueryRequest, tx ...*gorm.DB) ([]*dto.VersionDto, error) {
	var versions []*model.VersionModel
	db := d.buildQuery(d.getDb(tx).Model(&model.VersionModel{}), &request.VersionQueryRequest)
	db = db.Offset((request.Page - 1) * request.Size).Limit(request.Size)
	if err := db.Find(&versions).Error; err != nil {
		logger.Log().Error("分页查询版本失败: %v, request: %+v", err, request)
		return nil, err
	}
	return modelsToDto(versions), nil
}

func (d *versionDao) Count(request *dto.VersionQueryRequest, tx ...*gorm.DB) (int64, error) {
	var count int64
	db := d.buildQuery(d.getDb(tx).Model(&model.VersionModel{}), request)

	if err := db.Count(&count).Error; err != nil {
		logger.Log().Error("查询版本数量失败: %v, request: %+v", err, request)
		return 0, err
	}
	return count, nil
}
